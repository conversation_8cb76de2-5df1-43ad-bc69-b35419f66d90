namespace LibBusinessModules.UI.Fault
{
    partial class FrmFaultDataGenerator
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if(disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.uiGroupBox1 = new Sunny.UI.UIGroupBox();
            this.numDayRange = new Sunny.UI.UIIntegerUpDown();
            this.numDeviceCount = new Sunny.UI.UIIntegerUpDown();
            this.numRecordCount = new Sunny.UI.UIIntegerUpDown();
            this.uiLabel3 = new Sunny.UI.UILabel();
            this.uiLabel2 = new Sunny.UI.UILabel();
            this.uiLabel1 = new Sunny.UI.UILabel();
            this.uiGroupBox2 = new Sunny.UI.UIGroupBox();
            this.btnQuickGenerate = new Sunny.UI.UIButton();
            this.btnClear = new Sunny.UI.UIButton();
            this.btnGenerate = new Sunny.UI.UIButton();
            this.btnClose = new Sunny.UI.UIButton();
            this.uiLabel4 = new Sunny.UI.UILabel();
            this.uiGroupBox1.SuspendLayout();
            this.uiGroupBox2.SuspendLayout();
            this.SuspendLayout();
            // 
            // uiGroupBox1
            // 
            this.uiGroupBox1.Controls.Add(this.numDayRange);
            this.uiGroupBox1.Controls.Add(this.numDeviceCount);
            this.uiGroupBox1.Controls.Add(this.numRecordCount);
            this.uiGroupBox1.Controls.Add(this.uiLabel3);
            this.uiGroupBox1.Controls.Add(this.uiLabel2);
            this.uiGroupBox1.Controls.Add(this.uiLabel1);
            this.uiGroupBox1.Font = new System.Drawing.Font("宋体", 12F);
            this.uiGroupBox1.Location = new System.Drawing.Point(12, 47);
            this.uiGroupBox1.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.uiGroupBox1.MinimumSize = new System.Drawing.Size(1, 1);
            this.uiGroupBox1.Name = "uiGroupBox1";
            this.uiGroupBox1.Padding = new System.Windows.Forms.Padding(1, 32, 1, 1);
            this.uiGroupBox1.Size = new System.Drawing.Size(460, 150);
            this.uiGroupBox1.TabIndex = 0;
            this.uiGroupBox1.Text = "数据生成参数";
            this.uiGroupBox1.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // numDayRange
            // 
            this.numDayRange.Font = new System.Drawing.Font("宋体", 12F);
            this.numDayRange.Location = new System.Drawing.Point(150, 110);
            this.numDayRange.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.numDayRange.Maximum = 365;
            this.numDayRange.Minimum = 1;
            this.numDayRange.MinimumSize = new System.Drawing.Size(100, 0);
            this.numDayRange.Name = "numDayRange";
            this.numDayRange.ShowText = false;
            this.numDayRange.Size = new System.Drawing.Size(120, 29);
            this.numDayRange.TabIndex = 5;
            this.numDayRange.Text = "uiIntegerUpDown3";
            this.numDayRange.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            this.numDayRange.Value = 30;
            // 
            // numDeviceCount
            // 
            this.numDeviceCount.Font = new System.Drawing.Font("宋体", 12F);
            this.numDeviceCount.Location = new System.Drawing.Point(150, 75);
            this.numDeviceCount.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.numDeviceCount.Maximum = 100;
            this.numDeviceCount.Minimum = 5;
            this.numDeviceCount.MinimumSize = new System.Drawing.Size(100, 0);
            this.numDeviceCount.Name = "numDeviceCount";
            this.numDeviceCount.ShowText = false;
            this.numDeviceCount.Size = new System.Drawing.Size(120, 29);
            this.numDeviceCount.TabIndex = 4;
            this.numDeviceCount.Text = "uiIntegerUpDown2";
            this.numDeviceCount.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            this.numDeviceCount.Value = 25;
            // 
            // numRecordCount
            // 
            this.numRecordCount.Font = new System.Drawing.Font("宋体", 12F);
            this.numRecordCount.Location = new System.Drawing.Point(150, 40);
            this.numRecordCount.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.numRecordCount.Maximum = 1000;
            this.numRecordCount.Minimum = 10;
            this.numRecordCount.MinimumSize = new System.Drawing.Size(100, 0);
            this.numRecordCount.Name = "numRecordCount";
            this.numRecordCount.ShowText = false;
            this.numRecordCount.Size = new System.Drawing.Size(120, 29);
            this.numRecordCount.TabIndex = 3;
            this.numRecordCount.Text = "uiIntegerUpDown1";
            this.numRecordCount.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            this.numRecordCount.Value = 150;
            // 
            // uiLabel3
            // 
            this.uiLabel3.Font = new System.Drawing.Font("宋体", 12F);
            this.uiLabel3.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel3.Location = new System.Drawing.Point(20, 110);
            this.uiLabel3.Name = "uiLabel3";
            this.uiLabel3.Size = new System.Drawing.Size(120, 29);
            this.uiLabel3.TabIndex = 2;
            this.uiLabel3.Text = "时间范围(天)：";
            this.uiLabel3.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // uiLabel2
            // 
            this.uiLabel2.Font = new System.Drawing.Font("宋体", 12F);
            this.uiLabel2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel2.Location = new System.Drawing.Point(20, 75);
            this.uiLabel2.Name = "uiLabel2";
            this.uiLabel2.Size = new System.Drawing.Size(120, 29);
            this.uiLabel2.TabIndex = 1;
            this.uiLabel2.Text = "设备数量：";
            this.uiLabel2.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // uiLabel1
            // 
            this.uiLabel1.Font = new System.Drawing.Font("宋体", 12F);
            this.uiLabel1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel1.Location = new System.Drawing.Point(20, 40);
            this.uiLabel1.Name = "uiLabel1";
            this.uiLabel1.Size = new System.Drawing.Size(120, 29);
            this.uiLabel1.TabIndex = 0;
            this.uiLabel1.Text = "故障记录数：";
            this.uiLabel1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // uiGroupBox2
            // 
            this.uiGroupBox2.Controls.Add(this.btnQuickGenerate);
            this.uiGroupBox2.Controls.Add(this.btnClear);
            this.uiGroupBox2.Controls.Add(this.btnGenerate);
            this.uiGroupBox2.Font = new System.Drawing.Font("宋体", 12F);
            this.uiGroupBox2.Location = new System.Drawing.Point(12, 207);
            this.uiGroupBox2.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.uiGroupBox2.MinimumSize = new System.Drawing.Size(1, 1);
            this.uiGroupBox2.Name = "uiGroupBox2";
            this.uiGroupBox2.Padding = new System.Windows.Forms.Padding(1, 32, 1, 1);
            this.uiGroupBox2.Size = new System.Drawing.Size(460, 100);
            this.uiGroupBox2.TabIndex = 1;
            this.uiGroupBox2.Text = "操作";
            this.uiGroupBox2.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // btnQuickGenerate
            // 
            this.btnQuickGenerate.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnQuickGenerate.Font = new System.Drawing.Font("宋体", 12F);
            this.btnQuickGenerate.Location = new System.Drawing.Point(320, 45);
            this.btnQuickGenerate.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnQuickGenerate.Name = "btnQuickGenerate";
            this.btnQuickGenerate.Size = new System.Drawing.Size(120, 35);
            this.btnQuickGenerate.TabIndex = 2;
            this.btnQuickGenerate.Text = "快速生成";
            this.btnQuickGenerate.TipsFont = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnQuickGenerate.Click += new System.EventHandler(this.btnQuickGenerate_Click);
            // 
            // btnClear
            // 
            this.btnClear.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnClear.Font = new System.Drawing.Font("宋体", 12F);
            this.btnClear.Location = new System.Drawing.Point(170, 45);
            this.btnClear.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnClear.Name = "btnClear";
            this.btnClear.Size = new System.Drawing.Size(120, 35);
            this.btnClear.TabIndex = 1;
            this.btnClear.Text = "清空数据";
            this.btnClear.TipsFont = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnClear.Click += new System.EventHandler(this.btnClear_Click);
            // 
            // btnGenerate
            // 
            this.btnGenerate.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnGenerate.Font = new System.Drawing.Font("宋体", 12F);
            this.btnGenerate.Location = new System.Drawing.Point(20, 45);
            this.btnGenerate.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnGenerate.Name = "btnGenerate";
            this.btnGenerate.Size = new System.Drawing.Size(120, 35);
            this.btnGenerate.TabIndex = 0;
            this.btnGenerate.Text = "生成数据";
            this.btnGenerate.TipsFont = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnGenerate.Click += new System.EventHandler(this.btnGenerate_Click);
            // 
            // btnClose
            // 
            this.btnClose.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnClose.Font = new System.Drawing.Font("宋体", 12F);
            this.btnClose.Location = new System.Drawing.Point(352, 320);
            this.btnClose.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnClose.Name = "btnClose";
            this.btnClose.Size = new System.Drawing.Size(120, 35);
            this.btnClose.TabIndex = 2;
            this.btnClose.Text = "关闭";
            this.btnClose.TipsFont = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnClose.Click += new System.EventHandler(this.btnClose_Click);
            // 
            // uiLabel4
            // 
            this.uiLabel4.Font = new System.Drawing.Font("宋体", 10F);
            this.uiLabel4.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(128)))));
            this.uiLabel4.Location = new System.Drawing.Point(12, 320);
            this.uiLabel4.Name = "uiLabel4";
            this.uiLabel4.Size = new System.Drawing.Size(320, 35);
            this.uiLabel4.TabIndex = 3;
            this.uiLabel4.Text = "提示：生成的数据将包含已知和未识别的故障类型，\r\n用于测试故障统计报表的各项功能。";
            this.uiLabel4.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // FrmFaultDataGenerator
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.ClientSize = new System.Drawing.Size(484, 371);
            this.Controls.Add(this.uiLabel4);
            this.Controls.Add(this.btnClose);
            this.Controls.Add(this.uiGroupBox2);
            this.Controls.Add(this.uiGroupBox1);
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "FrmFaultDataGenerator";
            this.ShowIcon = false;
            this.ShowInTaskbar = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "故障数据生成器";
            this.ZoomScaleRect = new System.Drawing.Rectangle(15, 15, 484, 371);
            this.Load += new System.EventHandler(this.FrmFaultDataGenerator_Load);
            this.uiGroupBox1.ResumeLayout(false);
            this.uiGroupBox2.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private Sunny.UI.UIGroupBox uiGroupBox1;
        private Sunny.UI.UIIntegerUpDown numDayRange;
        private Sunny.UI.UIIntegerUpDown numDeviceCount;
        private Sunny.UI.UIIntegerUpDown numRecordCount;
        private Sunny.UI.UILabel uiLabel3;
        private Sunny.UI.UILabel uiLabel2;
        private Sunny.UI.UILabel uiLabel1;
        private Sunny.UI.UIGroupBox uiGroupBox2;
        private Sunny.UI.UIButton btnQuickGenerate;
        private Sunny.UI.UIButton btnClear;
        private Sunny.UI.UIButton btnGenerate;
        private Sunny.UI.UIButton btnClose;
        private Sunny.UI.UILabel uiLabel4;
    }
}

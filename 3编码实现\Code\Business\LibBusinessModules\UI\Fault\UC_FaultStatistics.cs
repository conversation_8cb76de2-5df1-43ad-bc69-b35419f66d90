﻿using LibBusinessModules.Config;
using LibBusinessModules.DB;
using LibBusinessModules.DB.Models.PC;
using LibBusinessModules.UI.Fault.Models;
using LiveChartsCore;
using LiveChartsCore.SkiaSharpView;
using LiveChartsCore.SkiaSharpView.Painting;
using LiveChartsCore.SkiaSharpView.SKCharts;
using SkiaSharp;
using Sunny.UI;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;
using System.Windows.Forms;

namespace LibBusinessModules.UI.Fault
{
    /// <summary>
    /// 故障统计界面
    /// </summary>
    public partial class UC_FaultStatistics : UIUserControl
    {
        #region 字段属性

        /// <summary>
        /// 查询起始时间
        /// </summary>
        private DateTime StartTime;

        /// <summary>
        /// 查询结束时间
        /// </summary>
        private DateTime EndTime;

        /// <summary>
        /// 界面是否初始化完成
        /// </summary>
        private bool _hasInit = false;

        /// <summary>
        /// 统计结果数据
        /// </summary>
        private List<FaultStatisticsItem> _statisticsData;

        #endregion

        #region 构造函数

        public UC_FaultStatistics()
        {
            InitializeComponent();
        }

        #endregion

        #region 事件处理

        private void UC_FaultRecordQuery_Load(object sender, EventArgs e)
        {
            if(!_hasInit)
            {
                _hasInit = true;
                InitialDateTimePicker();
                InitializeChartSettings();
            }
        }

        /// <summary>
        /// 查询按钮点击事件
        /// </summary>
        private void btnStartQuery_Click(object sender, EventArgs e)
        {
            StartTime = dtpStartTime.Value;
            EndTime = dtpEndTime.Value;

            Enabled = false;

            try
            {
                if(StartTime >= EndTime)
                {
                    throw new Exception("起始时间不能大于结束时间！");
                }

                QueryResult();
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError("查询数据错误:" + ex.Message);
            }

            Enabled = true;
        }

        /// <summary>
        /// 导出按钮点击事件
        /// </summary>
        private void btnExcelPNG_Click(object sender, EventArgs e)
        {
            try
            {
                Enabled = false;
                if(_statisticsData == null || _statisticsData.Count == 0)
                {
                    throw new Exception("无数据可导出！");
                }
                else
                {
                    // 导出时弹窗提示选取导出目录，及文件名称
                    string filePath = Application.StartupPath + "\\export\\";
                    if(!Directory.Exists(filePath))
                    {
                        Directory.CreateDirectory(filePath);
                    }

                    saveFileDialog.InitialDirectory = filePath;
                    saveFileDialog.FileName = $"{filePath}故障统计报告{StartTime:yyyy-MM-dd HH：mm：ss}--{EndTime:yyyy-MM-dd HH：mm：ss}.png";

                    if(saveFileDialog.ShowDialog() == DialogResult.OK)
                    {
                        // 显示等待界面
                        UIFormServiceHelper.ShowWaitForm(ParentForm, "正在导出图片，请稍候...");

                        // 使用LiveCharts的导出功能
                        ExportChartsToImage(saveFileDialog.FileName);

                        // 隐藏等待界面
                        UIFormServiceHelper.HideWaitForm(ParentForm);

                        if(UIMessageBox.ShowAsk("导出成功！是否定位到文件所在位置？"))
                        {
                            var psi = new ProcessStartInfo("Explorer.exe")
                            {
                                Arguments = "/e,/select," + saveFileDialog.FileName
                            };
                            // 打开导出文件所在位置
                            Process.Start(psi);
                        }
                    }
                }
            }
            catch(Exception ex)
            {
                // 隐藏等待界面
                UIFormServiceHelper.HideWaitForm(ParentForm);
                UIMessageBox.ShowError($"导出失败：{ex.Message}");
            }
            finally
            {
                Enabled = true;
                // 隐藏等待界面
                UIFormServiceHelper.HideWaitForm(ParentForm);
            }
        }

        /// <summary>
        /// 生成数据按钮点击事件
        /// </summary>
        private void btnGenerateData_Click(object sender, EventArgs e)
        {
            try
            {
                var frmGenerator = new FrmFaultDataGenerator();
                if(frmGenerator.ShowDialog() == DialogResult.OK)
                {
                    // 数据生成完成后，自动刷新统计
                    btnStartQuery_Click(sender, e);
                }
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError($"打开数据生成器失败：{ex.Message}");
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化图表设置，配置中文字体支持和固定标题
        /// </summary>
        private void InitializeChartSettings()
        {
            try
            {
                // 设置图表控件的编码，确保中文正确显示
                // 设置LiveCharts全局字体
                LiveCharts.Configure(config =>
                    config.HasGlobalSKTypeface(SKTypeface.FromFamilyName("Microsoft YaHei"))
                );

                // 设置固定的图表标题和Y轴
                SetFixedChartTitlesAndYAxes();
            }
            catch(Exception ex)
            {
                // 如果字体设置失败，记录错误但不影响程序运行
                UINotifierHelper.ShowNotifier($"图表初始化失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 设置固定的图表标题和Y轴
        /// </summary>
        private void SetFixedChartTitlesAndYAxes()
        {
            try
            {
                // 设置类别1图表标题
                category1Chart.Title = new LiveChartsCore.SkiaSharpView.VisualElements.LabelVisual
                {
                    Text = "按类别1统计故障率",
                    TextSize = 16,
                    Paint = new SolidColorPaint(SKColors.Black)
                    {
                        SKTypeface = SKTypeface.FromFamilyName("Microsoft YaHei")
                    }
                };

                // 设置类别1图表Y轴
                category1Chart.YAxes = new Axis[]
                {
                    new Axis
                    {
                        Name = "故障率(%)",
                        TextSize = 12,
                        MinLimit = 0,
                        LabelsPaint = new SolidColorPaint(SKColors.Black)
                        {
                            SKTypeface = SKTypeface.FromFamilyName("Microsoft YaHei")
                        },
                        NamePaint = new SolidColorPaint(SKColors.Black)
                        {
                            SKTypeface = SKTypeface.FromFamilyName("Microsoft YaHei")
                        },
                        SeparatorsPaint = new SolidColorPaint(SKColors.LightGray)
                    }
                };

                // 设置类别2图表标题
                category2Chart.Title = new LiveChartsCore.SkiaSharpView.VisualElements.LabelVisual
                {
                    Text = "按类别2统计故障率",
                    TextSize = 16,
                    Paint = new SolidColorPaint(SKColors.Black)
                    {
                        SKTypeface = SKTypeface.FromFamilyName("Microsoft YaHei")
                    }
                };

                // 设置类别2图表Y轴
                category2Chart.YAxes = new Axis[]
                {
                    new Axis
                    {
                        Name = "故障率(%)",
                        TextSize = 12,
                        MinLimit = 0,
                        LabelsPaint = new SolidColorPaint(SKColors.Black)
                        {
                            SKTypeface = SKTypeface.FromFamilyName("Microsoft YaHei")
                        },
                        NamePaint = new SolidColorPaint(SKColors.Black)
                        {
                            SKTypeface = SKTypeface.FromFamilyName("Microsoft YaHei")
                        },
                        SeparatorsPaint = new SolidColorPaint(SKColors.LightGray)
                    }
                };

                // 设置因子图表标题
                factorChart.Title = new LiveChartsCore.SkiaSharpView.VisualElements.LabelVisual
                {
                    Text = "按因子统计故障率",
                    TextSize = 16,
                    Paint = new SolidColorPaint(SKColors.Black)
                    {
                        SKTypeface = SKTypeface.FromFamilyName("Microsoft YaHei")
                    }
                };

                // 设置因子图表Y轴
                factorChart.YAxes = new Axis[]
                {
                    new Axis
                    {
                        Name = "故障率(%)",
                        TextSize = 12,
                        MinLimit = 0,
                        LabelsPaint = new SolidColorPaint(SKColors.Black)
                        {
                            SKTypeface = SKTypeface.FromFamilyName("Microsoft YaHei")
                        },
                        NamePaint = new SolidColorPaint(SKColors.Black)
                        {
                            SKTypeface = SKTypeface.FromFamilyName("Microsoft YaHei")
                        },
                        SeparatorsPaint = new SolidColorPaint(SKColors.LightGray)
                    }
                };

                // 设置总故障率图表标题
                totalChart.Title = new LiveChartsCore.SkiaSharpView.VisualElements.LabelVisual
                {
                    Text = "总故障率统计",
                    TextSize = 16,
                    Paint = new SolidColorPaint(SKColors.Black)
                    {
                        SKTypeface = SKTypeface.FromFamilyName("Microsoft YaHei")
                    }
                };
            }
            catch(Exception ex)
            {
                throw new Exception($"设置图表标题失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 初始化DateTimePicker控件的值
        /// </summary>
        private void InitialDateTimePicker()
        {
            dtpStartTime.Value = DateTime.Today;
            dtpEndTime.Value = DateTime.Today + new TimeSpan(23, 59, 59);
        }

        /// <summary>
        /// 得到查询结果并更新界面
        /// 计算并显示统计结果
        /// </summary>
        private void QueryResult()
        {
            try
            {
                UIFormServiceHelper.ShowWaitForm(this.ParentForm, "数据统计中，请稍候...");

                // 计算统计数据
                CalculateFaultStatistics();

                // 显示统计结果
                DisplayStatisticsResults();
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError($"统计计算失败：{ex.Message}");
            }
            finally
            {
                // 隐藏进度条界面
                UIFormServiceHelper.HideWaitForm(this.ParentForm);
            }
        }

        /// <summary>
        /// 计算故障统计数据
        /// </summary>
        /// <returns>统计结果列表</returns>
        private void CalculateFaultStatistics()
        {
            var statisticsItems = new List<FaultStatisticsItem>();

            // 获取指定时间段内的故障记录
            var faultRecords = DBHelper.GetPCDBContext().Queryable<FaultRecordData>()
                     .Where(data => data.RecordTime >= StartTime && data.RecordTime <= EndTime)
                     .ToList();

            // 获取所有设备信息
            var allDevices = DBHelper.GetPCDBContext().Queryable<DeviceInfo>().ToList();

            // 计算按类别1统计的故障率
            var category1Statistics = CalculateCategory1Statistics(faultRecords, allDevices);
            statisticsItems.AddRange(category1Statistics);

            // 计算按类别2统计的故障率
            var category2Statistics = CalculateCategory2Statistics(faultRecords, allDevices);
            statisticsItems.AddRange(category2Statistics);

            // 计算按因子统计的故障率
            var factorStatistics = CalculateFactorStatistics(faultRecords, allDevices);
            statisticsItems.AddRange(factorStatistics);

            // 计算总故障率
            var totalStatistics = CalculateTotalStatistics(faultRecords, allDevices);
            statisticsItems.Add(totalStatistics);

            _statisticsData = statisticsItems;
        }

        /// <summary>
        /// 计算按类别1统计的故障率
        /// </summary>
        /// <param name="faultRecords">故障记录</param>
        /// <param name="allDevices">所有设备</param>
        /// <returns>类别1统计结果</returns>
        private List<FaultStatisticsItem> CalculateCategory1Statistics(List<FaultRecordData> faultRecords, List<DeviceInfo> allDevices)
        {
            var statistics = new List<FaultStatisticsItem>();
            var faultManager = FaultManager.GetInstance();
            var category1List = faultManager.GetAllCategory1();
            var totalDeviceCount = allDevices.Count;

            // 统计已知类别1的故障
            var knownCategory1Ids = new HashSet<int>();
            foreach(var category1 in category1List)
            {
                knownCategory1Ids.Add(category1.FaultId);
                var faultCount = faultRecords.Count(f => f.Category1Id == category1.FaultId);
                var faultRate = totalDeviceCount > 0 ? (double)faultCount / totalDeviceCount * 100 : 0;

                statistics.Add(new FaultStatisticsItem
                {
                    StatisticsType = "类别1",
                    ItemName = category1.FaultName,
                    FaultCount = faultCount,
                    TotalDeviceCount = totalDeviceCount,
                    FaultRate = faultRate
                });
            }

            // 统计未识别的类别1故障
            var unrecognizedCount = faultRecords.Count(f => f.Category1Id > 0 && !knownCategory1Ids.Contains(f.Category1Id));
            if(unrecognizedCount > 0)
            {
                var unrecognizedRate = totalDeviceCount > 0 ? (double)unrecognizedCount / totalDeviceCount * 100 : 0;
                statistics.Add(new FaultStatisticsItem
                {
                    StatisticsType = "类别1",
                    ItemName = "未识别",
                    FaultCount = unrecognizedCount,
                    TotalDeviceCount = totalDeviceCount,
                    FaultRate = unrecognizedRate
                });
            }

            return statistics;
        }

        /// <summary>
        /// 计算按类别2统计的故障率
        /// </summary>
        /// <param name="faultRecords">故障记录</param>
        /// <param name="allDevices">所有设备</param>
        /// <returns>类别2统计结果</returns>
        private List<FaultStatisticsItem> CalculateCategory2Statistics(List<FaultRecordData> faultRecords, List<DeviceInfo> allDevices)
        {
            var statistics = new List<FaultStatisticsItem>();
            var faultManager = FaultManager.GetInstance();
            var category1List = faultManager.GetAllCategory1();
            var totalDeviceCount = allDevices.Count;

            // 统计已知类别2的故障
            var knownCategory2Ids = new HashSet<int>();
            foreach(var category1 in category1List)
            {
                foreach(var category2 in category1.SubCategories)
                {
                    knownCategory2Ids.Add(category2.FaultId);
                    var faultCount = faultRecords.Count(f => f.Category2Id == category2.FaultId);
                    var faultRate = totalDeviceCount > 0 ? (double)faultCount / totalDeviceCount * 100 : 0;

                    statistics.Add(new FaultStatisticsItem
                    {
                        StatisticsType = "类别2",
                        ItemName = $"{category1.FaultName}-{category2.FaultName}",
                        FaultCount = faultCount,
                        TotalDeviceCount = totalDeviceCount,
                        FaultRate = faultRate
                    });
                }
            }

            // 统计未识别的类别2故障
            var unrecognizedCount = faultRecords.Count(f => f.Category2Id > 0 && !knownCategory2Ids.Contains(f.Category2Id));
            if(unrecognizedCount > 0)
            {
                var unrecognizedRate = totalDeviceCount > 0 ? (double)unrecognizedCount / totalDeviceCount * 100 : 0;
                statistics.Add(new FaultStatisticsItem
                {
                    StatisticsType = "类别2",
                    ItemName = "未识别",
                    FaultCount = unrecognizedCount,
                    TotalDeviceCount = totalDeviceCount,
                    FaultRate = unrecognizedRate
                });
            }

            return statistics;
        }

        /// <summary>
        /// 计算按因子统计的故障率
        /// </summary>
        /// <param name="faultRecords">故障记录</param>
        /// <param name="allDevices">所有设备</param>
        /// <returns>因子统计结果</returns>
        private List<FaultStatisticsItem> CalculateFactorStatistics(List<FaultRecordData> faultRecords, List<DeviceInfo> allDevices)
        {
            var statistics = new List<FaultStatisticsItem>();

            // 按因子分组统计
            var factorGroups = new Dictionary<string, List<string>>();
            var factorDeviceCounts = new Dictionary<string, int>();
            var allDeviceSNCodes = new HashSet<string>();

            // 遍历所有设备，按因子分组
            foreach(var device in allDevices)
            {
                allDeviceSNCodes.Add(device.SNCode);
                try
                {
                    var factor = GetFactorFromSNCode(device.SNCode);
                    if(!factorGroups.ContainsKey(factor))
                    {
                        factorGroups[factor] = new List<string>();
                        factorDeviceCounts[factor] = 0;
                    }
                    factorGroups[factor].Add(device.SNCode);
                    factorDeviceCounts[factor]++;
                }
                catch
                {
                    // 忽略获取因子失败的设备
                }
            }

            // 计算每个因子的故障率
            foreach(var factorGroup in factorGroups)
            {
                var factor = factorGroup.Key;
                var deviceSNCodes = factorGroup.Value;
                var totalDeviceCount = factorDeviceCounts[factor];

                // 统计该因子下的故障数量
                var faultCount = faultRecords.Count(f => deviceSNCodes.Contains(f.SNCode));
                var faultRate = totalDeviceCount > 0 ? (double)faultCount / totalDeviceCount * 100 : 0;

                statistics.Add(new FaultStatisticsItem
                {
                    StatisticsType = "因子",
                    ItemName = factor,
                    FaultCount = faultCount,
                    TotalDeviceCount = totalDeviceCount,
                    FaultRate = faultRate
                });
            }

            // 统计未识别因子的故障（设备序列号不在已知设备列表中的故障）
            var unrecognizedFaults = faultRecords.Where(f => !allDeviceSNCodes.Contains(f.SNCode)).ToList();
            if(unrecognizedFaults.Count > 0)
            {
                var unrecognizedRate = allDevices.Count > 0 ? (double)unrecognizedFaults.Count / allDevices.Count * 100 : 0;
                statistics.Add(new FaultStatisticsItem
                {
                    StatisticsType = "因子",
                    ItemName = "未识别",
                    FaultCount = unrecognizedFaults.Count,
                    TotalDeviceCount = allDevices.Count,
                    FaultRate = unrecognizedRate
                });
            }

            return statistics.OrderBy(s => s.ItemName).ToList();
        }

        /// <summary>
        /// 计算总故障率
        /// </summary>
        /// <param name="faultRecords">故障记录</param>
        /// <param name="allDevices">所有设备</param>
        /// <returns>总故障率统计结果</returns>
        private FaultStatisticsItem CalculateTotalStatistics(List<FaultRecordData> faultRecords, List<DeviceInfo> allDevices)
        {
            var totalDeviceCount = allDevices.Count;

            // 统计有故障的设备数量（去重）
            var faultDeviceSNCodes = faultRecords.Select(f => f.SNCode).Distinct().ToList();
            var faultDeviceCount = faultDeviceSNCodes.Count;

            var totalFaultRate = totalDeviceCount > 0 ? (double)faultDeviceCount / totalDeviceCount * 100 : 0;

            return new FaultStatisticsItem
            {
                StatisticsType = "总计",
                ItemName = "总故障率",
                FaultCount = faultDeviceCount,
                TotalDeviceCount = totalDeviceCount,
                FaultRate = totalFaultRate
            };
        }

        /// <summary>
        /// 根据设备序列号获取因子信息
        /// </summary>
        /// <param name="snCode">设备序列号</param>
        /// <returns>因子信息</returns>
        private string GetFactorFromSNCode(string snCode)
        {
            if(string.IsNullOrEmpty(snCode))
                return "未知";

            try
            {
                // 根据序列号前6位获取设备配置
                if(snCode.Length >= 6)
                {
                    string idCode = snCode.Substring(0, 6);
                    var deviceConfig = DeviceManager.GetInstance().GetDeviceConfig(idCode);
                    if(deviceConfig != null)
                    {
                        return deviceConfig.Factor;
                    }
                }
            }
            catch(Exception)
            {
                // 记录错误但不影响显示
            }

            return "未知";
        }

        /// <summary>
        /// 显示统计结果
        /// </summary>
        private void DisplayStatisticsResults()
        {
            if(_statisticsData == null || _statisticsData.Count == 0)
            {
                throw new Exception("指定时间段内没有故障数据！");
            }

            // 更新所有图表
            UpdateCategory1Chart();
            UpdateCategory2Chart();
            UpdateFactorChart();
            UpdateTotalChart();
        }

        /// <summary>
        /// 更新类别1图表
        /// </summary>
        private void UpdateCategory1Chart()
        {
            var category1Data = _statisticsData.Where(x => x.StatisticsType == "类别1").ToList();
            if(category1Data.Count == 0) return;

            try
            {
                var series = new ColumnSeries<double>
                {
                    Values = category1Data.Select(x => x.FaultRate).ToArray(),
                    Name = "故障率(%)",
                    Fill = new SolidColorPaint(SKColors.DodgerBlue),
                    Stroke = new SolidColorPaint(SKColors.DarkBlue) { StrokeThickness = 1 },
                    DataLabelsSize = 12,
                    DataLabelsPaint = new SolidColorPaint(SKColors.Black)
                    {
                        SKTypeface = SKTypeface.FromFamilyName("Microsoft YaHei")
                    },
                    DataLabelsPosition = LiveChartsCore.Measure.DataLabelsPosition.Top,
                    DataLabelsFormatter = (point) =>
                    {
                        var index = (int)point.Context.Entity.MetaData!.EntityIndex;
                        var item = category1Data[index];
                        return $"{point.Coordinate.PrimaryValue:F1}% ({item.FaultCount}/{item.TotalDeviceCount})";
                    }
                };

                category1Chart.Series = new ISeries[] { series };
                category1Chart.XAxes = new Axis[]
                {
                    new Axis
                    {
                        Labels = category1Data.Select(x => x.ItemName).ToArray(),
                        LabelsRotation = -45,
                        TextSize = 12,
                        LabelsPaint = new SolidColorPaint(SKColors.Black)
                        {
                            SKTypeface = SKTypeface.FromFamilyName("Microsoft YaHei")
                        },
                        SeparatorsPaint = new SolidColorPaint(SKColors.LightGray)
                    }
                };
            }
            catch(Exception ex)
            {
                // 如果LiveCharts有问题，显示错误信息
                UIMessageBox.ShowError($"更新类别1图表失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 更新类别2图表
        /// </summary>
        private void UpdateCategory2Chart()
        {
            var category2Data = _statisticsData.Where(x => x.StatisticsType == "类别2").ToList();
            if(category2Data.Count == 0) return;

            try
            {
                var series = new ColumnSeries<double>
                {
                    Values = category2Data.Select(x => x.FaultRate).ToArray(),
                    Name = "故障率(%)",
                    Fill = new SolidColorPaint(SKColors.Crimson),
                    Stroke = new SolidColorPaint(SKColors.DarkRed) { StrokeThickness = 1 },
                    DataLabelsSize = 12,
                    DataLabelsPaint = new SolidColorPaint(SKColors.Black)
                    {
                        SKTypeface = SKTypeface.FromFamilyName("Microsoft YaHei")
                    },
                    DataLabelsPosition = LiveChartsCore.Measure.DataLabelsPosition.Top,
                    DataLabelsFormatter = (point) =>
                    {
                        var index = point.Context.Entity.MetaData!.EntityIndex;
                        var item = category2Data[index];
                        return $"{point.Coordinate.PrimaryValue:F1}% ({item.FaultCount}/{item.TotalDeviceCount})";
                    }
                };

                category2Chart.Series = new ISeries[] { series };
                category2Chart.XAxes = new Axis[]
                {
                    new Axis
                    {
                        Labels = category2Data.Select(x => x.ItemName.Length > 10 ? x.ItemName.Substring(0, 10) + "..." : x.ItemName).ToArray(),
                        LabelsRotation = -45,
                        TextSize = 12,
                        LabelsPaint = new SolidColorPaint(SKColors.Black)
                        {
                            SKTypeface = SKTypeface.FromFamilyName("Microsoft YaHei")
                        },
                        SeparatorsPaint = new SolidColorPaint(SKColors.LightGray)
                    }
                };
            }
            catch(Exception ex)
            {
                throw new Exception($"更新类别2图表失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 更新因子图表
        /// </summary>
        private void UpdateFactorChart()
        {
            var factorData = _statisticsData.Where(x => x.StatisticsType == "因子").ToList();
            if(factorData.Count == 0) return;

            try
            {
                var series = new ColumnSeries<double>
                {
                    Values = factorData.Select(x => x.FaultRate).ToArray(),
                    Name = "故障率(%)",
                    Fill = new SolidColorPaint(SKColors.Orange),
                    Stroke = new SolidColorPaint(SKColors.DarkOrange) { StrokeThickness = 1 },
                    DataLabelsSize = 12,
                    DataLabelsPaint = new SolidColorPaint(SKColors.Black)
                    {
                        SKTypeface = SKTypeface.FromFamilyName("Microsoft YaHei")
                    },
                    DataLabelsPosition = LiveChartsCore.Measure.DataLabelsPosition.Top,
                    DataLabelsFormatter = (point) =>
                    {
                        var index = point.Context.Entity.MetaData!.EntityIndex;
                        var item = factorData[index];
                        return $"{point.Coordinate.PrimaryValue:F1}% ({item.FaultCount}/{item.TotalDeviceCount})";
                    }
                };

                factorChart.Series = new ISeries[] { series };
                factorChart.XAxes = new Axis[]
                {
                    new Axis
                    {
                        Labels = factorData.Select(x => x.ItemName).ToArray(),
                        LabelsRotation = -45,
                        TextSize = 12,
                        LabelsPaint = new SolidColorPaint(SKColors.Black)
                        {
                            SKTypeface = SKTypeface.FromFamilyName("Microsoft YaHei")
                        },
                        SeparatorsPaint = new SolidColorPaint(SKColors.LightGray)
                    }
                };
            }
            catch(Exception ex)
            {
                throw new Exception($"更新因子图表失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 更新总故障率图表
        /// </summary>
        private void UpdateTotalChart()
        {
            var totalData = _statisticsData.Where(x => x.StatisticsType == "总计").FirstOrDefault();
            if(totalData == null) return;

            try
            {
                var faultRate = totalData.FaultCount;
                var normalRate = totalData.TotalDeviceCount - totalData.FaultCount;

                var series = new PieSeries<double>[]
                {
                    new PieSeries<double>
                    {
                        Values = new double[] { faultRate },
                        Name = "故障设备",
                        Fill = new SolidColorPaint(SKColors.Crimson),
                        Stroke = new SolidColorPaint(SKColors.White) { StrokeThickness = 2 },
                        DataLabelsSize = 14,
                        DataLabelsPaint = new SolidColorPaint(SKColors.White)
                        {
                            SKTypeface = SKTypeface.FromFamilyName("Microsoft YaHei")
                        },
                        DataLabelsPosition = LiveChartsCore.Measure.PolarLabelsPosition.Middle,
                        DataLabelsFormatter = (point) => $"{point.Coordinate.PrimaryValue:F0}"
                    },
                    new PieSeries<double>
                    {
                        Values = new double[] { normalRate },
                        Name = "正常设备",
                        Fill = new SolidColorPaint(SKColors.LimeGreen),
                        Stroke = new SolidColorPaint(SKColors.White) { StrokeThickness = 2 },
                        DataLabelsSize = 14,
                        DataLabelsPaint = new SolidColorPaint(SKColors.White)
                        {
                            SKTypeface = SKTypeface.FromFamilyName("Microsoft YaHei")
                        },
                        DataLabelsPosition = LiveChartsCore.Measure.PolarLabelsPosition.Middle,
                        DataLabelsFormatter = (point) => $"{point.Coordinate.PrimaryValue:F0}"
                    }
                };

                totalChart.Series = series;

                // 更新图表标题以显示设备数量信息
                totalChart.Title = new LiveChartsCore.SkiaSharpView.VisualElements.LabelVisual
                {
                    Text = $"总故障率统计 ({totalData.FaultRate:F1}%)",
                    TextSize = 16,
                    Paint = new SolidColorPaint(SKColors.Black)
                    {
                        SKTypeface = SKTypeface.FromFamilyName("Microsoft YaHei")
                    }
                };

                // 设置图例
                totalChart.LegendPosition = LiveChartsCore.Measure.LegendPosition.Bottom;
                totalChart.LegendTextPaint = new SolidColorPaint(SKColors.Black)
                {
                    SKTypeface = SKTypeface.FromFamilyName("Microsoft YaHei")
                };
                totalChart.LegendTextSize = 12;
            }
            catch(Exception ex)
            {
                throw new Exception($"更新总故障率图表失败：{ex.Message}");
            }
        }

        #region PNG导出功能

        /// <summary>
        /// 导出图表到图片文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        private void ExportChartsToImage(string filePath)
        {
            try
            {
                // 创建一个大的画布来容纳所有图表
                int canvasWidth = 1200;
                int canvasHeight = 800;

                using(var bitmap = new Bitmap(canvasWidth, canvasHeight))
                using(var graphics = Graphics.FromImage(bitmap))
                {
                    graphics.Clear(Color.White);

                    // 绘制标题
                    using(var titleFont = new Font("微软雅黑", 16, FontStyle.Bold))
                    using(var titleBrush = new SolidBrush(Color.Black))
                    {
                        string title = $"故障统计报告{StartTime:yyyy-MM-dd HH：mm：ss}--{EndTime:yyyy-MM-dd HH：mm：ss}";
                        var titleSize = graphics.MeasureString(title, titleFont);
                        graphics.DrawString(title, titleFont, titleBrush,
                            (canvasWidth - titleSize.Width) / 2, 20);
                    }

                    // 导出各个图表为小图片并合成
                    int chartWidth = 580;
                    int chartHeight = 350;

                    // 类别1图表
                    if(category1Chart.Series != null && category1Chart.Series.Any())
                    {
                        var skChart = new SKCartesianChart(category1Chart) { Width = chartWidth, Height = chartHeight };
                        using(var chartImage = skChart.GetImage())
                        using(var data = chartImage.Encode())
                        using(var stream = new MemoryStream(data.ToArray()))
                        using(var chart1Image = new Bitmap(stream))
                        {
                            graphics.DrawImage(chart1Image, 10, 70);
                        }
                    }

                    // 类别2图表
                    if(category2Chart.Series != null && category2Chart.Series.Any())
                    {
                        var skChart = new SKCartesianChart(category2Chart) { Width = chartWidth, Height = chartHeight };
                        using(var chartImage = skChart.GetImage())
                        using(var data = chartImage.Encode())
                        using(var stream = new MemoryStream(data.ToArray()))
                        using(var chart2Image = new Bitmap(stream))
                        {
                            graphics.DrawImage(chart2Image, 610, 70);
                        }
                    }

                    // 因子图表
                    if(factorChart.Series != null && factorChart.Series.Any())
                    {
                        var skChart = new SKCartesianChart(factorChart) { Width = chartWidth, Height = chartHeight };
                        using(var chartImage = skChart.GetImage())
                        using(var data = chartImage.Encode())
                        using(var stream = new MemoryStream(data.ToArray()))
                        using(var chart3Image = new Bitmap(stream))
                        {
                            graphics.DrawImage(chart3Image, 10, 430);
                        }
                    }

                    // 总故障率图表
                    if(totalChart.Series != null && totalChart.Series.Any())
                    {
                        var skChart = new SKPieChart(totalChart) { Width = chartWidth, Height = chartHeight };
                        using(var chartImage = skChart.GetImage())
                        using(var data = chartImage.Encode())
                        using(var stream = new MemoryStream(data.ToArray()))
                        using(var chart4Image = new Bitmap(stream))
                        {
                            graphics.DrawImage(chart4Image, 610, 430);
                        }
                    }

                    // 保存合成的图片
                    bitmap.Save(filePath, ImageFormat.Png);
                }
            }
            catch(Exception ex)
            {
                throw new Exception($"导出图表图片失败：{ex.Message}", ex);
            }
        }

        #endregion

        #endregion
    }
}
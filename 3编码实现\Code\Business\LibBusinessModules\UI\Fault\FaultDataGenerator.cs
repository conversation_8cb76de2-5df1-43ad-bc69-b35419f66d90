using LibBusinessModules.Config;
using LibBusinessModules.Config.FaultManagement.Models;
using LibBusinessModules.DB;
using LibBusinessModules.DB.Models.PC;
using Sunny.UI;
using System;
using System.Collections.Generic;
using System.Linq;

namespace LibBusinessModules.UI.Fault
{
    /// <summary>
    /// 故障数据生成器 - 用于生成测试数据
    /// </summary>
    public class FaultDataGenerator
    {
        #region 字段属性

        private readonly Random _random = new Random();
        private List<string> _deviceSNCodes = new List<string>();
        private List<FaultCategory1> _category1List = new List<FaultCategory1>();
        private List<FaultCategory2> _category2List = new List<FaultCategory2>();

        #endregion

        #region 构造函数

        public FaultDataGenerator()
        {
            InitializeData();
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 生成测试数据并插入数据库
        /// </summary>
        /// <param name="recordCount">要生成的故障记录数量</param>
        /// <param name="deviceCount">要生成的设备数量</param>
        /// <param name="dayRange">时间范围（天数）</param>
        public void GenerateTestData(int recordCount = 150, int deviceCount = 25, int dayRange = 30)
        {
            try
            {
                UIFormServiceHelper.ShowWaitForm(null, "正在生成测试数据，请稍候...");

                // 1. 生成设备序列号
                GenerateDeviceSNCodes(deviceCount, dayRange);

                // 2. 生成故障记录
                var faultRecords = GenerateFaultRecords(recordCount, dayRange);

                // 3. 插入数据库
                InsertFaultRecords(faultRecords);

                UIFormServiceHelper.HideWaitForm(null);
                UIMessageBox.ShowSuccess($"成功生成 {faultRecords.Count} 条故障记录数据！\n设备数量：{_deviceSNCodes.Count}\n时间范围：最近 {dayRange} 天");
            }
            catch(Exception ex)
            {
                UIFormServiceHelper.HideWaitForm(null);
                UIMessageBox.ShowError($"生成测试数据失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 清空所有故障记录数据
        /// </summary>
        public void ClearAllFaultRecords()
        {
            try
            {
                if(UIMessageBox.ShowAsk("确定要清空所有故障记录数据吗？此操作不可恢复！"))
                {
                    UIFormServiceHelper.ShowWaitForm(null, "正在清空数据，请稍候...");

                    var db = DBHelper.GetPCDBContext();
                    db.Deleteable<FaultRecordData>().ExecuteCommand();

                    UIFormServiceHelper.HideWaitForm(null);
                    UIMessageBox.ShowSuccess("故障记录数据已清空！");
                }
            }
            catch(Exception ex)
            {
                UIFormServiceHelper.HideWaitForm(null);
                UIMessageBox.ShowError($"清空数据失败：{ex.Message}");
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化基础数据
        /// </summary>
        private void InitializeData()
        {
            var faultManager = FaultManager.GetInstance();
            _category1List = faultManager.GetAllCategory1();

            // 收集所有二级分类
            _category2List.Clear();
            foreach(var category1 in _category1List)
            {
                _category2List.AddRange(category1.SubCategories);
            }
        }

        /// <summary>
        /// 生成设备序列号列表
        /// </summary>
        /// <param name="deviceCount">设备数量</param>
        private void GenerateDeviceSNCodes(int deviceCount, int dayRange)
        {
            _deviceSNCodes.Clear();
            var deviceManager = DeviceManager.GetInstance();
            var deviceConfigs = deviceManager.GetDeviceList();
            var startTime = DateTime.Now.AddDays(-dayRange);
            var endTime = DateTime.Now;

            // 预定义的因子类型和对应的ID码
            var factorIdCodes = new Dictionary<string, string>
            {
                { "COD", "FA0101" },
                { "NH4", "FA0102" },
                { "TN", "FA0103" },
                { "TP", "FA0104" },
                { "IMN", "FA0105" }
            };

            // 如果配置中没有设备，使用预定义的
            if(deviceConfigs.Count == 0)
            {
                foreach(var factor in factorIdCodes)
                {
                    for(int i = 1; i <= Math.Max(1, deviceCount / factorIdCodes.Count); i++)
                    {
                        var time = GetRandomDateTime(startTime, endTime);
                        string snCode = $"{factor.Value}{time:yyyyMMdd}{i:D3}";
                        _deviceSNCodes.Add(snCode);
                        if(_deviceSNCodes.Count >= deviceCount) break;
                    }
                    if(_deviceSNCodes.Count >= deviceCount) break;
                }
            }
            else
            {
                // 使用配置中的设备信息生成序列号
                foreach(var config in deviceConfigs)
                {
                    for(int i = 1; i <= Math.Max(1, deviceCount / deviceConfigs.Count); i++)
                    {
                        var time = GetRandomDateTime(startTime, endTime);
                        string snCode = $"{config.IdCode}{time:yyyyMMdd}{i:D3}";
                        _deviceSNCodes.Add(snCode);
                        if(_deviceSNCodes.Count >= deviceCount) break;
                    }
                    if(_deviceSNCodes.Count >= deviceCount) break;
                }
            }

            // 确保有足够的设备
            while(_deviceSNCodes.Count < deviceCount)
            {
                var time = GetRandomDateTime(startTime, endTime);
                string snCode = $"FA0199{time:yyyyMMdd}{_deviceSNCodes.Count:D3}";
                _deviceSNCodes.Add(snCode);
            }
        }

        /// <summary>
        /// 生成故障记录列表
        /// </summary>
        /// <param name="recordCount">记录数量</param>
        /// <param name="dayRange">时间范围（天数）</param>
        /// <returns>故障记录列表</returns>
        private List<FaultRecordData> GenerateFaultRecords(int recordCount, int dayRange)
        {
            var faultRecords = new List<FaultRecordData>();
            var startTime = DateTime.Now.AddDays(-dayRange);
            var endTime = DateTime.Now;

            // 故障描述模板
            var problemDescriptions = new[]
            {
                "设备显示异常，数值波动较大",
                "传感器响应缓慢，测量精度下降",
                "设备自检失败，需要重新校准",
                "通信连接不稳定，数据传输中断",
                "温度补偿功能异常，影响测量结果",
                "流路堵塞，样品无法正常进入",
                "电源模块故障，设备无法正常启动",
                "显示屏出现花屏现象",
                "按键响应异常，操作困难",
                "数据存储模块故障，历史数据丢失"
            };

            for(int i = 0; i < recordCount; i++)
            {
                var faultRecord = new FaultRecordData
                {
                    SNCode = GetRandomDeviceSNCode(),
                    RecordTime = GetRandomDateTime(startTime, endTime),
                    Category1Id = GetRandomCategory1Id(),
                    Category2Id = 0, // 将在下面设置
                    ProblemDescription = problemDescriptions[_random.Next(problemDescriptions.Length)]
                };

                // 根据Category1Id设置对应的Category2Id
                faultRecord.Category2Id = GetRandomCategory2Id(faultRecord.Category1Id);

                faultRecords.Add(faultRecord);
            }

            return faultRecords;
        }

        /// <summary>
        /// 获取随机设备序列号
        /// </summary>
        /// <returns>设备序列号</returns>
        private string GetRandomDeviceSNCode()
        {
            if(_deviceSNCodes.Count == 0)
                return "FA010124001";

            return _deviceSNCodes[_random.Next(_deviceSNCodes.Count)];
        }

        /// <summary>
        /// 获取随机时间
        /// </summary>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns>随机时间</returns>
        private DateTime GetRandomDateTime(DateTime startTime, DateTime endTime)
        {
            var timeSpan = endTime - startTime;
            var randomTimeSpan = new TimeSpan((long)(_random.NextDouble() * timeSpan.Ticks));
            return startTime + randomTimeSpan;
        }

        /// <summary>
        /// 获取随机的一级分类ID（包含未识别类型）
        /// </summary>
        /// <returns>一级分类ID</returns>
        private int GetRandomCategory1Id()
        {
            // 80%概率使用已知分类，20%概率使用未识别分类
            if(_random.NextDouble() < 0.8 && _category1List.Count > 0)
            {
                var category1 = _category1List[_random.Next(_category1List.Count)];
                return category1.FaultId;
            }
            else
            {
                // 生成未识别的分类ID（使用较大的数值）
                return _random.Next(9001, 9999);
            }
        }

        /// <summary>
        /// 根据一级分类ID获取对应的二级分类ID
        /// </summary>
        /// <param name="category1Id">一级分类ID</param>
        /// <returns>二级分类ID</returns>
        private int GetRandomCategory2Id(int category1Id)
        {
            // 查找对应的一级分类
            var category1 = _category1List.FirstOrDefault(c => c.FaultId == category1Id);

            if(category1 != null && category1.SubCategories.Count > 0)
            {
                // 80%概率使用已知的二级分类，20%概率使用未识别分类
                if(_random.NextDouble() < 0.8)
                {
                    var category2 = category1.SubCategories[_random.Next(category1.SubCategories.Count)];
                    return category2.FaultId;
                }
            }

            // 生成未识别的二级分类ID
            return _random.Next(8001, 8999);
        }

        /// <summary>
        /// 将故障记录插入数据库
        /// </summary>
        /// <param name="faultRecords">故障记录列表</param>
        private void InsertFaultRecords(List<FaultRecordData> faultRecords)
        {
            if(faultRecords.Count == 0)
                return;

            var db = DBHelper.GetPCDBContext();

            // 批量插入数据
            db.Insertable(faultRecords).ExecuteCommand();
        }

        #endregion

        #region 静态方法

        /// <summary>
        /// 快速生成测试数据的静态方法
        /// </summary>
        /// <param name="recordCount">故障记录数量</param>
        /// <param name="deviceCount">设备数量</param>
        /// <param name="dayRange">时间范围（天数）</param>
        public static void QuickGenerate(int recordCount = 150, int deviceCount = 25, int dayRange = 30)
        {
            var generator = new FaultDataGenerator();
            generator.GenerateTestData(recordCount, deviceCount, dayRange);
        }

        /// <summary>
        /// 快速清空所有故障记录数据
        /// </summary>
        public static void QuickClear()
        {
            var generator = new FaultDataGenerator();
            generator.ClearAllFaultRecords();
        }

        #endregion
    }
}
